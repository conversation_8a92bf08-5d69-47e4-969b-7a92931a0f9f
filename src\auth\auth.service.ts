import { Injectable } from "@nestjs/common";
import { JwtService } from "@nestjs/jwt";
import { UsersService } from "src/users/users.service";
import { IAuth } from "./interfaces/auth.interfaces";
import { IUser } from "src/users/interfaces/users.interface";
import { CreateUserDto } from "src/users/dto/createUser.dto";
import { signInDto } from "./dto/signIn.dto";
import * as bcrypt from 'bcrypt';

@Injectable()
export class AuthService{
    constructor(private readonly usersService:UsersService,
        private readonly jwtService:JwtService){}

        async signUp(signUpDto:CreateUserDto):Promise<IAuth>{
            const user: Omit<IUser,"password"> = await this.usersService.createUser(signUpDto);
            const payload={name:user.firstName,_id:user._id};
            const accessToken=await this.jwtService.signAsync(payload);
            return {
                ...user,
                accessToken
            };
        }

        async signIn(signInDto:signInDto):Promise<IAuth>{
            const user=await this.usersService.findByEmail(signInDto.email.toLowerCase());
            if(!user){
                throw new Error("Invalid credentials");
            }
            const userWithPassword=await this.usersService.findByEmailWithPassword(signInDto.email.toLowerCase());
            const isMatch=await bcrypt.compare(signInDto.password,userWithPassword.password);
            if(!isMatch){
                throw new Error("Invalid credentials");
            }
            const payload={name:user.firstName,_id:user._id};
            const accessToken=await this.jwtService.signAsync(payload);
            return {
                ...user,
                accessToken
            };
           
        }
}