import { Body, Controller, Post } from "@nestjs/common";
import { AuthService } from "./auth.service";
import { IAuth } from "./interfaces/auth.interfaces";
import { CreateUserDto } from "src/users/dto/createUser.dto";
import { signInDto } from "./dto/signIn.dto";
import {
    ApiTags,
    ApiOperation,
    ApiResponse,
    ApiBody,
    ApiBadRequestResponse,
    ApiConflictResponse,
    ApiUnauthorizedResponse
} from '@nestjs/swagger';
import { AuthResponseDto } from './dto/auth-response.dto';
import { ErrorResponseDto, ValidationErrorResponseDto, UnauthorizedErrorResponseDto } from '../common/dto/error-response.dto';

@ApiTags('auth')
@Controller('auth')
export class AuthController{
    constructor(private readonly authService:AuthService){}

    @Post("signup")
    @ApiOperation({
        summary: 'User registration',
        description: 'Register a new user account and receive an authentication token',
    })
    @ApiBody({
        type: CreateUserDto,
        description: 'User registration data',
    })
    @ApiResponse({
        status: 201,
        description: 'User registered successfully',
        type: AuthResponseDto,
    })
    @ApiBadRequestResponse({
        description: 'Validation error or invalid input data',
        type: ValidationErrorResponseDto,
    })
    @ApiConflictResponse({
        description: 'User already exists with this email or phone number',
        type: ErrorResponseDto,
        schema: {
            example: {
                statusCode: 409,
                message: 'User already exists',
                error: 'Conflict',
                timestamp: '2024-01-15T10:30:00.000Z',
                path: '/api/auth/signup'
            }
        }
    })
    async signUp(@Body() signUpDto:CreateUserDto):Promise<IAuth>{
        return this.authService.signUp(signUpDto);
    }

    @Post("signin")
    @ApiOperation({
        summary: 'User login',
        description: 'Authenticate user credentials and receive an authentication token',
    })
    @ApiBody({
        type: signInDto,
        description: 'User login credentials',
    })
    @ApiResponse({
        status: 200,
        description: 'User authenticated successfully',
        type: AuthResponseDto,
    })
    @ApiBadRequestResponse({
        description: 'Validation error or invalid input data',
        type: ValidationErrorResponseDto,
    })
    @ApiUnauthorizedResponse({
        description: 'Invalid credentials',
        type: UnauthorizedErrorResponseDto,
    })
    async signIn(@Body() signInDto:signInDto):Promise<IAuth>{
        return this.authService.signIn(signInDto);
    }
}