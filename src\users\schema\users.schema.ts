import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, SchemaFactory } from '@nestjs/mongoose';

@Schema({ timestamps: true })
export class Users {
 @Prop({ required: true })
 firstName: string;

 @Prop({ required: true })
 lastName: string;

 @Prop({ required: true })
 email: string;

 @Prop({ required: true })
 password: string;

 @Prop({required:true})
 mobileNumber:string;

 @Prop({required:true})
 gender:string;

 @Prop({required:true})
 dob:string;

 @Prop({required:false,default:false})
 isVerified:boolean;
}

export const UsersSchema = SchemaFactory.createForClass(Users);
