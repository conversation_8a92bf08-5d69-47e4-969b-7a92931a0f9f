import { Body, Controller, Get, Param, Post } from "@nestjs/common";
import { UsersService } from "./users.service";
import {
    ApiTags,
    ApiOperation,
    ApiResponse,
} from '@nestjs/swagger';
import { UserResponseDto } from './dto/user-response.dto';
import { ErrorResponseDto } from '../common/dto/error-response.dto';

@ApiTags('users')
@Controller('users')
export class UsersController{
    constructor(private readonly usersService:UsersService){}    
@Get(':id')
@ApiOperation({
    summary: 'Get user by ID',
    description: 'Retrieve user information by their unique identifier',
})
@ApiResponse({
    status: 200,
    description: 'User found',
    type: UserResponseDto,
})
@ApiResponse({
    status: 404,
    description: 'User not found',
    type: ErrorResponseDto,
})
async getUserById(@Param('id') id: string) {
    return this.usersService.findUserById(id);
}
}