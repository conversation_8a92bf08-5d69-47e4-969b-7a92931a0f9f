import { HttpException, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Users } from './schema/users.schema';
import { IUser } from './interfaces/users.interface';
import { Model } from 'mongoose';
import * as bcrypt from 'bcrypt';
import { CreateUserDto } from './dto/createUser.dto';
@Injectable()
export class UsersService {
  constructor(@InjectModel(Users.name) private userModel: Model<IUser>) {}

  async createUser(createUserDto:CreateUserDto):Promise<Omit<IUser,"password">>{
    const email=createUserDto.email.toLowerCase();
    const existingUser=await this.userModel.find({email});
    if(existingUser.length){
        throw new Error("User already exists");
    }
    const existingPhone=await this.userModel.find({mobileNumber:createUserDto.mobileNumber});
    if(existingPhone.length){
        throw new Error("Phone number already exists");
    }
    const salt=await bcrypt.genSalt();
    const hashedPassword=await bcrypt.hash(createUserDto.password,salt);
    const user=await this.userModel.create({
        ...createUserDto,
        password:hashedPassword
    });
    const{password,...userWithoutPassword}=user.toObject();
    return {
        ...userWithoutPassword
    }
  }

  async findByEmail(email:string):Promise <Omit<IUser,"password">>{
    const user= await this.userModel.findOne({email}).exec();
    if(!user){
      throw new HttpException('User not found',404);
    }
    const {password,...userWithoutPassword}=user.toObject();
    return {
        ...userWithoutPassword
    }
  }

  async findByEmailWithPassword(email: string): Promise<IUser | null> {
    return this.userModel.findOne({ email }).select('+password').exec();
  }

  async findUserById(id:string):Promise<Omit<IUser,"password">>{
    const user=await this.userModel.findById(id).exec();
    if(!user){
        throw new HttpException('User not found',404);
    }
    const {password,...userWithoutPassword}=user.toObject();
    return {
        ...userWithoutPassword
    }
  }
}
