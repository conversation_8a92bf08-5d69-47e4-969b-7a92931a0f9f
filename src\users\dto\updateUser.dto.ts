import { PartialType } from "@nestjs/swagger";
import { CreateUserDto } from "./createUser.dto";
import { ApiProperty } from '@nestjs/swagger';

export class UpdateUserDto extends PartialType(CreateUserDto){
    @ApiProperty({
        description: 'User\'s first name (optional for update)',
        example: '<PERSON>',
        required: false,
        minLength: 1,
        maxLength: 50,
    })
    firstName?: string;

    @ApiProperty({
        description: 'User\'s last name (optional for update)',
        example: 'Doe',
        required: false,
        minLength: 1,
        maxLength: 50,
    })
    lastName?: string;

    @ApiProperty({
        description: 'User\'s email address (optional for update)',
        example: '<EMAIL>',
        required: false,
        format: 'email',
    })
    email?: string;

    @ApiProperty({
        description: 'User\'s mobile phone number (optional for update)',
        example: '+1234567890',
        required: false,
        pattern: '^[+]?[1-9]\\d{1,14}$',
    })
    mobileNumber?: string;

    @ApiProperty({
        description: 'User\'s gender (optional for update)',
        example: 'Male',
        required: false,
        enum: ['Male', 'Female', 'Other'],
    })
    gender?: string;

    @ApiProperty({
        description: 'User\'s date of birth (optional for update)',
        example: '1990-01-15',
        required: false,
        format: 'date',
    })
    dob?: string;
};